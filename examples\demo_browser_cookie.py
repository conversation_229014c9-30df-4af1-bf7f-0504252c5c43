#!/usr/bin/env python3
"""
Demo script showing how to use browser cookie authentication

This script demonstrates how to:
1. Set up browser cookie authentication
2. Test the authentication
3. Retrieve Confluence spaces successfully even with 2FA

Usage:
1. Login to Confluence in your browser
2. Extract JSESSIONID cookie (see instructions below)
3. Run: set CONFLUENCE_BROWSER_COOKIE=<cookie_value> && python demo_browser_cookie.py
"""

import os
import sys
from datetime import datetime

# Add current directory to path to import client
sys.path.insert(0, os.path.dirname(__file__))

from client import AtlassianClient, ConfluenceAPI
from config import ATLASSIAN_CONFIG


def show_cookie_extraction_instructions():
    """Show detailed instructions for extracting browser cookie"""
    print("🍪 HOW TO EXTRACT BROWSER COOKIE")
    print("=" * 50)
    print()
    print("1. Open your browser and login to Confluence")
    print(f"   URL: {ATLASSIAN_CONFIG['CONFLUENCE_URL']}")
    print()
    print("2. Open Developer Tools:")
    print("   - Chrome/Edge: Press F12 or Ctrl+Shift+I")
    print("   - Firefox: Press F12 or Ctrl+Shift+I")
    print()
    print("3. Navigate to the Application/Storage tab:")
    print("   - Chrome/Edge: Application → Storage → Cookies")
    print("   - Firefox: Storage → Cookies")
    print()
    print("4. Find your Confluence domain and look for:")
    print("   - JSESSIONID")
    print("   - CONFLUENCESESSIONID")
    print("   - Any cookie with 'session' in the name")
    print()
    print("5. Copy the cookie VALUE (not the name)")
    print("   Example: ABC123DEF456GHI789...")
    print()
    print("6. Set the environment variable:")
    print("   Windows: set CONFLUENCE_BROWSER_COOKIE=ABC123DEF456GHI789...")
    print("   Linux/Mac: export CONFLUENCE_BROWSER_COOKIE=ABC123DEF456GHI789...")
    print()
    print("7. Run this script again")
    print()


def demo_with_browser_cookie():
    """Demonstrate browser cookie authentication"""
    print("🚀 BROWSER COOKIE AUTHENTICATION DEMO")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check for browser cookie
    browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
    
    if not browser_cookie:
        print("❌ No CONFLUENCE_BROWSER_COOKIE environment variable found!")
        print()
        show_cookie_extraction_instructions()
        return False
    
    print(f"✅ Browser cookie found: {browser_cookie[:20]}...")
    print()
    
    try:
        # Initialize client with session auth
        print("🔧 Initializing client with session authentication...")
        client = AtlassianClient(ATLASSIAN_CONFIG, auth_method='session')
        confluence = ConfluenceAPI(client)
        print("   ✓ Client initialized")
        print()
        
        # Test authentication
        print("🔐 Testing authentication...")
        auth_success = client._authenticate_confluence_session()
        if auth_success:
            print("   ✓ Authentication successful")
        else:
            print("   ✗ Authentication failed")
            return False
        print()
        
        # Test spaces retrieval
        print("📚 Retrieving Confluence spaces...")
        spaces = confluence.get_spaces(limit=10)
        
        if 'results' in spaces:
            space_count = len(spaces['results'])
            print(f"   ✅ Successfully retrieved {space_count} spaces!")
            print()
            
            if space_count > 0:
                print("📁 Available spaces:")
                for i, space in enumerate(spaces['results']):
                    print(f"   {i+1:2d}. {space['key']:<15} - {space['name']}")
                print()
                
                # Test getting pages from first space
                first_space = spaces['results'][0]
                print(f"📄 Getting pages from space: {first_space['key']}")
                
                try:
                    pages = confluence.get_pages(space_key=first_space['key'], limit=5)
                    if 'results' in pages:
                        page_count = len(pages['results'])
                        print(f"   ✅ Found {page_count} pages in {first_space['key']}")
                        
                        for i, page in enumerate(pages['results']):
                            print(f"      {i+1}. {page['title']}")
                    else:
                        print("   ⚠️  No pages found or access restricted")
                except Exception as e:
                    print(f"   ⚠️  Could not retrieve pages: {e}")
                
                print()
                
                # Test search functionality
                print("🔍 Testing search functionality...")
                try:
                    search_results = confluence.search_content('type=page', limit=3)
                    if 'results' in search_results:
                        result_count = len(search_results['results'])
                        print(f"   ✅ Search returned {result_count} results")
                    else:
                        print("   ⚠️  Search returned no results")
                except Exception as e:
                    print(f"   ⚠️  Search failed: {e}")
                
            else:
                print("   ⚠️  No spaces found - user may not have access to any spaces")
        else:
            print("   ❌ Invalid response format")
            return False
        
        print()
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Browser cookie authentication is working")
        print("✅ Confluence API access is functional")
        print("✅ You can now use this method in your applications")
        print()
        print("💡 Tips for production use:")
        print("   - Store the cookie securely")
        print("   - Monitor cookie expiration")
        print("   - Implement cookie renewal logic")
        print("   - Handle authentication errors gracefully")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print()
        print("🔧 Troubleshooting:")
        print("   - Verify the cookie value is correct")
        print("   - Check if the cookie has expired")
        print("   - Ensure you're logged into Confluence in the browser")
        print("   - Try extracting a fresh cookie")
        return False


def demo_without_browser_cookie():
    """Show what happens without browser cookie"""
    print("⚠️  DEMO WITHOUT BROWSER COOKIE")
    print("=" * 50)
    print()
    
    try:
        client = AtlassianClient(ATLASSIAN_CONFIG, auth_method='session')
        confluence = ConfluenceAPI(client)
        
        print("🔧 Testing session authentication without browser cookie...")
        
        # This will likely fail with 2FA
        spaces = confluence.get_spaces(limit=5)
        print("   ✅ Surprisingly, it worked! (2FA might not be enabled)")
        
    except Exception as e:
        print(f"   ❌ Failed as expected: {e}")
        print("   💡 This is normal when 2FA is enabled")
        print()
        show_cookie_extraction_instructions()


if __name__ == "__main__":
    browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
    
    if browser_cookie:
        success = demo_with_browser_cookie()
        sys.exit(0 if success else 1)
    else:
        demo_without_browser_cookie()
        sys.exit(1)
