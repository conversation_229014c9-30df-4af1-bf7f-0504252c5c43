#!/usr/bin/env python3
"""
Quick test script for Confluence session cookie authentication

This is a minimal test to verify that session cookie authentication works
before running the full confluence_usage.py example.

Usage:
1. Set CONFLUENCE_BROWSER_COOKIE environment variable
2. Run: python test_confluence_session.py
"""

import sys
import os
from datetime import datetime

# Add paths to import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from extended_atlassian_client import ExtendedAtlassianClient
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

def quick_test():
    """Quick test of Confluence session authentication"""
    print("🧪 Quick Confluence Session Test")
    print("="*40)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check for browser cookie
    browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
    
    if not browser_cookie:
        print("❌ CONFLUENCE_BROWSER_COOKIE environment variable not set!")
        print()
        print("Please set it with your browser cookie:")
        print("  Windows: set CONFLUENCE_BROWSER_COOKIE=your_cookie_value")
        print("  Linux/Mac: export CONFLUENCE_BROWSER_COOKIE=your_cookie_value")
        return False
    
    print(f"✅ Cookie found: {browser_cookie[:15]}...")
    print()
    
    try:
        # Create client
        print("🔧 Creating client...")
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        print("   ✓ Client created")
        
        # Test connection
        print("🔗 Testing connection...")
        results = client.test_connection()
        
        if results.get('confluence'):
            print("   ✅ Connection successful!")
        else:
            print("   ❌ Connection failed!")
            return False
        
        # Quick spaces test
        print("📚 Testing spaces retrieval...")
        spaces = client.confluence.get_spaces(limit=3)
        
        space_count = len(spaces.get('results', []))
        print(f"   ✅ Retrieved {space_count} spaces")
        
        if space_count > 0:
            print("   Sample spaces:")
            for space in spaces.get('results', [])[:2]:
                key = space.get('key', 'N/A')
                name = space.get('name', 'N/A')
                print(f"     • {key}: {name}")
        
        print()
        print("🎉 All tests passed! You can now run confluence_usage.py")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print()
        print("Troubleshooting tips:")
        print("1. Verify your cookie is still valid (re-extract from browser)")
        print("2. Check that CONFLUENCE_URL in config.py is correct")
        print("3. Ensure you have access to Confluence from this machine")
        return False

if __name__ == "__main__":
    success = quick_test()
    if not success:
        sys.exit(1)
