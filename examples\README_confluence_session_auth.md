# Confluence Session Cookie Authentication Example

This example demonstrates how to use session cookie authentication to connect to Confluence when 2FA (Two-Factor Authentication) is enabled, which typically blocks API token authentication.

## Problem

When Confluence has 2FA enabled (like Microsoft Authenticator), traditional API token authentication often fails with 401 Unauthorized errors. This is because the API tokens cannot satisfy the 2FA requirement.

## Solution

Use session cookie authentication by extracting the JSESSIONID cookie from your browser after logging in manually. This bypasses the 2FA requirement since you've already authenticated through the web interface.

## Quick Start

### 1. Extract Browser Cookie

1. **Login to Confluence** in your browser normally (with 2FA if required)
2. **Open Developer Tools** (F12)
3. **Navigate to Application/Storage tab** → Cookies
4. **Find your Confluence domain** in the cookies list
5. **Look for cookie named** `CONFLUENCESESSIONID` or `JSESSIONID`
6. **Copy the cookie value** (long string of characters)

### 2. Set Environment Variable

**Windows (Command Prompt):**
```cmd
set CONFLUENCE_BROWSER_COOKIE=your_cookie_value_here
```

**Windows (PowerShell):**
```powershell
$env:CONFLUENCE_BROWSER_COOKIE="your_cookie_value_here"
```

**Linux/Mac:**
```bash
export CONFLUENCE_BROWSER_COOKIE=your_cookie_value_here
```

### 3. Run the Example

```bash
cd examples
python confluence_usage.py
```

## What the Example Does

The `confluence_usage.py` script demonstrates:

1. **Connection Testing** - Verifies the session cookie works
2. **Retrieve All Spaces** - Gets a list of all Confluence spaces you have access to
3. **Space Details** - Shows detailed information about a specific space
4. **Content Search** - Searches for pages within a space

## Expected Output

```
🌐 Confluence Usage Examples with Session Cookie Authentication
======================================================================
Started at: 2024-01-15 14:30:25

✅ Browser cookie found: ABC123DEF456GHI789...

🔗 Testing Confluence Connection
========================================
✅ Connection successful!

======================================================================
📚 Getting All Confluence Spaces (limit: 20)
========================================
Found 5 spaces:

 1. PROJ - Project Documentation
    Type: global
    Description: Main project documentation space
    Homepage: Welcome to Project Docs

 2. TEAM - Team Space
    Type: global
    Description: Team collaboration and meeting notes
    Homepage: Team Home

...
```

## Configuration

Make sure your `config.py` file has the correct Confluence URL and is set to use session authentication:

```python
ATLASSIAN_CONFIG = {
    "CONFLUENCE_URL": "https://your-confluence-server.com",
    "CONFLUENCE_USERNAME": "<EMAIL>",
    "CONFLUENCE_API_TOKEN": "not_used_with_session_auth",
    # ... other config
}

AUTH_METHOD = 'session'  # Important: Use session auth

CLIENT_SETTINGS = {
    'timeout': 30,
    'verify_ssl': True,
    'auth_method': AUTH_METHOD
}
```

## Troubleshooting

### Cookie Not Working
- **Check cookie name**: Some Confluence instances use `JSESSIONID` instead of `CONFLUENCESESSIONID`
- **Verify domain**: Make sure you're copying the cookie from the correct domain
- **Check expiration**: Browser cookies expire; you may need to re-extract them

### Connection Fails
- **Verify URL**: Ensure `CONFLUENCE_URL` in config.py matches your browser URL
- **Check network**: Ensure you can access Confluence from your machine
- **SSL issues**: Try setting `verify_ssl: False` in CLIENT_SETTINGS for testing

### No Spaces Returned
- **Check permissions**: You may not have access to any spaces
- **Try different endpoints**: The example includes multiple API calls to test different access levels

## Security Notes

- **Keep cookies secure**: Don't share or commit cookie values to version control
- **Cookie expiration**: Browser cookies expire and need to be refreshed periodically
- **Limited scope**: This method only works while your browser session is valid

## Alternative Methods

If session cookies don't work, consider:
1. **Personal Access Tokens** (if supported by your Confluence version)
2. **App passwords** (for some Atlassian Cloud instances)
3. **OAuth 2.0** (for more complex integrations)

## Files in This Example

- `confluence_usage.py` - Main example script
- `README_confluence_session_auth.md` - This documentation
- `../config.py` - Configuration file (update with your settings)
- `../client/atlassian_client.py` - Core client with session auth support
