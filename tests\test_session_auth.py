#!/usr/bin/env python3
"""
Test script specifically for session authentication and Confluence spaces retrieval

This test validates that:
1. Session authentication works correctly
2. Confluence spaces can be retrieved using session auth
3. The client handles session authentication edge cases properly
"""

import logging
import sys
import os
import unittest
from unittest.mock import patch, MagicMock
import requests

# Add parent directory to path to import client
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from client import AtlassianClient, ConfluenceAPI, AuthenticationError, APIError
from config import ATLASSIAN_CONFIG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestSessionAuthentication(unittest.TestCase):
    """Test cases for session authentication"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = ATLASSIAN_CONFIG.copy()
        self.client_settings = {
            'timeout': 30,
            'verify_ssl': True,
            'auth_method': 'session'
        }
    
    def test_session_auth_initialization(self):
        """Test that client initializes correctly with session auth"""
        try:
            client = AtlassianClient(self.config, **self.client_settings)
            self.assertEqual(client.auth_method, 'session')
            self.assertFalse(client.confluence_session_active)
            self.assertFalse(client.jira_session_active)
            logger.info("✓ Session auth client initialization successful")
        except Exception as e:
            self.fail(f"Session auth client initialization failed: {e}")
    
    def test_confluence_session_authentication(self):
        """Test Confluence session authentication"""
        client = AtlassianClient(self.config, **self.client_settings)
        
        # Test session authentication
        try:
            auth_success = client._authenticate_confluence_session()
            if auth_success:
                self.assertTrue(client.confluence_session_active)
                logger.info("✓ Confluence session authentication successful")
            else:
                logger.warning("⚠ Confluence session authentication failed - this may be expected with 2FA")
                # This is not necessarily a test failure if 2FA is enabled
        except Exception as e:
            logger.error(f"✗ Confluence session authentication error: {e}")
            # Don't fail the test here as this might be expected with 2FA
    
    def test_confluence_browser_cookie_auth(self):
        """Test Confluence authentication using browser cookie"""
        # Test with mock browser cookie
        with patch.dict(os.environ, {'CONFLUENCE_BROWSER_COOKIE': 'mock_session_id'}):
            client = AtlassianClient(self.config, **self.client_settings)
            
            # This should succeed with the mock cookie
            auth_success = client._authenticate_confluence_session()
            self.assertTrue(auth_success)
            self.assertTrue(client.confluence_session_active)
            logger.info("✓ Browser cookie authentication setup successful")
    
    def test_confluence_spaces_retrieval_with_session_auth(self):
        """Test retrieving Confluence spaces using session authentication"""
        client = AtlassianClient(self.config, **self.client_settings)
        confluence = ConfluenceAPI(client)
        
        try:
            # Attempt to get spaces
            spaces = confluence.get_spaces(limit=5)
            
            # Validate response structure
            self.assertIsInstance(spaces, dict)
            self.assertIn('results', spaces)
            self.assertIsInstance(spaces['results'], list)
            
            # Log results
            space_count = len(spaces['results'])
            logger.info(f"✓ Successfully retrieved {space_count} Confluence spaces")
            
            if space_count > 0:
                # Test first space details
                first_space = spaces['results'][0]
                self.assertIn('key', first_space)
                self.assertIn('name', first_space)
                logger.info(f"  First space: {first_space['key']} - {first_space['name']}")
            
            return spaces
            
        except AuthenticationError as e:
            logger.warning(f"⚠ Authentication failed: {e}")
            logger.info("This may be expected if 2FA is enabled and no browser cookie is provided")
            self.skipTest("Authentication failed - likely due to 2FA requirements")
        except APIError as e:
            logger.error(f"✗ API error while retrieving spaces: {e}")
            self.fail(f"API error: {e}")
        except Exception as e:
            logger.error(f"✗ Unexpected error while retrieving spaces: {e}")
            self.fail(f"Unexpected error: {e}")
    
    def test_confluence_specific_space_retrieval(self):
        """Test retrieving a specific Confluence space"""
        client = AtlassianClient(self.config, **self.client_settings)
        confluence = ConfluenceAPI(client)
        
        try:
            # First get available spaces
            spaces = confluence.get_spaces(limit=5)
            
            if not spaces['results']:
                self.skipTest("No spaces available to test specific space retrieval")
            
            # Get the first space in detail
            space_key = spaces['results'][0]['key']
            space_details = confluence.get_space(space_key, expand='description,homepage')
            
            # Validate response
            self.assertIsInstance(space_details, dict)
            self.assertEqual(space_details['key'], space_key)
            self.assertIn('name', space_details)
            
            logger.info(f"✓ Successfully retrieved space details for: {space_key}")
            
        except AuthenticationError as e:
            logger.warning(f"⚠ Authentication failed: {e}")
            self.skipTest("Authentication failed - likely due to 2FA requirements")
        except Exception as e:
            logger.error(f"✗ Error retrieving specific space: {e}")
            self.fail(f"Error retrieving specific space: {e}")
    
    def test_session_persistence(self):
        """Test that session persists across multiple requests"""
        client = AtlassianClient(self.config, **self.client_settings)
        confluence = ConfluenceAPI(client)
        
        try:
            # Make first request
            spaces1 = confluence.get_spaces(limit=2)
            
            # Make second request - should reuse session
            spaces2 = confluence.get_spaces(limit=2)
            
            # Both should succeed
            self.assertIsInstance(spaces1, dict)
            self.assertIsInstance(spaces2, dict)
            
            logger.info("✓ Session persistence across multiple requests successful")
            
        except AuthenticationError as e:
            logger.warning(f"⚠ Authentication failed: {e}")
            self.skipTest("Authentication failed - likely due to 2FA requirements")
        except Exception as e:
            logger.error(f"✗ Session persistence test failed: {e}")
            self.fail(f"Session persistence test failed: {e}")
    
    def test_connection_test_with_session_auth(self):
        """Test the connection test functionality with session auth"""
        client = AtlassianClient(self.config, **self.client_settings)
        
        try:
            connection_status = client.test_connection()
            
            # Validate response structure
            self.assertIsInstance(connection_status, dict)
            self.assertIn('confluence', connection_status)
            self.assertIn('jira', connection_status)
            
            # Log results
            confluence_status = connection_status['confluence']
            jira_status = connection_status['jira']
            
            logger.info(f"Connection test results:")
            logger.info(f"  Confluence: {'✓' if confluence_status else '✗'}")
            logger.info(f"  Jira: {'✓' if jira_status else '✗'}")
            
            # At least one should work for the test to be meaningful
            if not confluence_status and not jira_status:
                logger.warning("⚠ Both Confluence and Jira connections failed")
                logger.info("This may be expected if 2FA is enabled and no browser cookies are provided")
            
        except Exception as e:
            logger.error(f"✗ Connection test failed: {e}")
            self.fail(f"Connection test failed: {e}")


def test_with_browser_cookie():
    """Test session authentication with browser cookie if available"""
    logger.info("\n" + "=" * 60)
    logger.info("TESTING WITH BROWSER COOKIE (if available)")
    logger.info("=" * 60)

    # Check if browser cookie is available
    browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
    if not browser_cookie:
        logger.info("ℹ No CONFLUENCE_BROWSER_COOKIE environment variable found")
        logger.info("ℹ To test with browser cookie:")
        logger.info("  1. Login to Confluence in your browser")
        logger.info("  2. Extract JSESSIONID cookie value")
        logger.info("  3. Set environment variable: CONFLUENCE_BROWSER_COOKIE=<cookie_value>")
        logger.info("  4. Re-run this test")
        return False

    logger.info(f"✓ Found browser cookie: {browser_cookie[:20]}...")

    # Test with browser cookie
    config = ATLASSIAN_CONFIG.copy()
    client_settings = {
        'timeout': 30,
        'verify_ssl': True,
        'auth_method': 'session'
    }

    try:
        client = AtlassianClient(config, **client_settings)
        confluence = ConfluenceAPI(client)

        # Test spaces retrieval
        spaces = confluence.get_spaces(limit=5)

        logger.info(f"✓ Successfully retrieved {len(spaces['results'])} spaces using browser cookie")

        if spaces['results']:
            for i, space in enumerate(spaces['results'][:3]):
                logger.info(f"  {i+1}. {space['key']} - {space['name']}")

        return True

    except Exception as e:
        logger.error(f"✗ Browser cookie test failed: {e}")
        return False


def run_session_auth_tests():
    """Run all session authentication tests"""
    logger.info("Starting Session Authentication Test Suite")
    logger.info("=" * 60)

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSessionAuthentication)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)

    # Test with browser cookie if available
    browser_cookie_success = test_with_browser_cookie()

    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("SESSION AUTHENTICATION TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Skipped: {len(result.skipped)}")
    logger.info(f"Browser cookie test: {'✓ Passed' if browser_cookie_success else '- Not available/failed'}")

    if result.failures:
        logger.info("\nFAILURES:")
        for test, traceback in result.failures:
            logger.info(f"  - {test}: {traceback}")

    if result.errors:
        logger.info("\nERRORS:")
        for test, traceback in result.errors:
            logger.info(f"  - {test}: {traceback}")

    success = len(result.failures) == 0 and len(result.errors) == 0
    if success:
        logger.info("\n✓ All session authentication tests passed!")
        if browser_cookie_success:
            logger.info("✓ Browser cookie authentication also working!")
    else:
        logger.info("\n⚠ Some session authentication tests failed or had errors")

    return success


if __name__ == "__main__":
    try:
        success = run_session_auth_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during testing: {e}")
        sys.exit(1)
