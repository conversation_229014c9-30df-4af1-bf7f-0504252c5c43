// Simplified Atlassian Cookie Extractor - Only .bat file download
class CookieExtractor {
    constructor() {
        this.cookies = {
            confluence: null,
            jira: null
        };
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        document.getElementById('extractAll').addEventListener('click', () => {
            this.extractAllCookies();
        });

        document.getElementById('downloadBat').addEventListener('click', () => {
            this.downloadBatFile();
        });

        document.getElementById('copyConfluence').addEventListener('click', () => {
            this.copyCookieToClipboard('confluence');
        });

        document.getElementById('copyJira').addEventListener('click', () => {
            this.copyCookieToClipboard('jira');
        });
    }

    async extractAllCookies() {
        const button = document.getElementById('extractAll');
        const status = document.getElementById('extractStatus');
        const results = document.getElementById('cookieResults');

        try {
            button.disabled = true;
            button.textContent = 'Extracting...';

            this.showStatus(status, 'info', 'Extracting cookies from both services...');

            // Extract Confluence cookie
            const confluenceCookie = await this.getCookie('https://confluence.smals.be', 'CONFLUENCESESSIONID');

            // Extract Jira cookie
            const jiraCookie = await this.getCookie('https://jira.smals.be', 'JSESSIONID');

            let extractedCount = 0;

            // Handle Confluence cookie
            if (confluenceCookie && confluenceCookie.value) {
                this.cookies.confluence = confluenceCookie.value;
                this.displayCookie('confluence', confluenceCookie.value);
                await this.storeCookie('confluence', confluenceCookie.value);
                extractedCount++;
            } else {
                this.hideCookie('confluence');
            }

            // Handle Jira cookie
            if (jiraCookie && jiraCookie.value) {
                this.cookies.jira = jiraCookie.value;
                this.displayCookie('jira', jiraCookie.value);
                await this.storeCookie('jira', jiraCookie.value);
                extractedCount++;
            } else {
                this.hideCookie('jira');
            }

            // Show results
            if (extractedCount > 0) {
                this.showStatus(status, 'success', `✅ Extracted ${extractedCount} cookie(s) successfully!`);
                results.style.display = 'block';
                this.updateActionButtons();
            } else {
                this.showStatus(status, 'error', '❌ No session cookies found. Please login to Confluence/Jira first.');
                results.style.display = 'none';
            }

        } catch (error) {
            console.error('Cookie extraction error:', error);
            this.showStatus(status, 'error', `❌ Error: ${error.message}`);
            results.style.display = 'none';
        } finally {
            button.disabled = false;
            button.textContent = 'Extract All Cookies';
        }
    }

    getCookie(url, name) {
        return new Promise((resolve, reject) => {
            chrome.cookies.get({
                url: url,
                name: name
            }, (cookie) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(cookie);
                }
            });
        });
    }

    showStatus(statusElement, type, message) {
        statusElement.className = `status ${type}`;
        statusElement.textContent = message;
        statusElement.style.display = 'block';
    }

    displayCookie(service, cookieValue) {
        const resultElement = document.getElementById(`${service}Result`);
        const valueElement = document.getElementById(`${service}Value`);

        valueElement.textContent = cookieValue;
        resultElement.style.display = 'block';
    }

    hideCookie(service) {
        const resultElement = document.getElementById(`${service}Result`);
        resultElement.style.display = 'none';
    }

    async copyCookieToClipboard(service) {
        const cookieValue = this.cookies[service];
        const copyButton = document.getElementById(`copy${service.charAt(0).toUpperCase() + service.slice(1)}`);

        if (!cookieValue) {
            this.showTemporaryMessage(copyButton, '❌ No cookie', 'error');
            return;
        }

        try {
            await navigator.clipboard.writeText(cookieValue);
            this.showTemporaryMessage(copyButton, '✅ Copied!', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            this.showTemporaryMessage(copyButton, '❌ Failed', 'error');
        }
    }

    showTemporaryMessage(button, message, type) {
        const originalText = button.textContent;
        const originalBackground = button.style.background;

        button.textContent = message;
        button.style.background = type === 'success'
            ? 'rgba(76, 175, 80, 0.5)'
            : 'rgba(244, 67, 54, 0.5)';

        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = originalBackground;
        }, 2000);
    }

    async storeCookie(service, cookieValue) {
        return new Promise((resolve) => {
            const key = `${service}_cookie`;
            chrome.storage.local.set({
                [key]: cookieValue,
                [`${key}_timestamp`]: Date.now()
            }, resolve);
        });
    }

    updateActionButtons() {
        const hasAnyCookie = this.cookies.confluence || this.cookies.jira;
        document.getElementById('downloadBat').style.display = hasAnyCookie ? 'inline-block' : 'none';
    }

    downloadBatFile() {
        const batScript = this.generateBatchScript();
        this.downloadFile(batScript, 'setup_atlassian_cookies.bat', 'text/plain');
        
        // Show success message
        const downloadBtn = document.getElementById('downloadBat');
        const originalText = downloadBtn.textContent;
        downloadBtn.textContent = '✅ Downloaded!';
        downloadBtn.style.background = 'rgba(76, 175, 80, 0.3)';
        
        setTimeout(() => {
            downloadBtn.textContent = originalText;
            downloadBtn.style.background = '';
        }, 2000);
    }

    generateBatchScript() {
        const lines = [
            '@echo off',
            'echo ================================================',
            'echo  Atlassian Cookie Environment Setup',
            'echo ================================================',
            'echo.',
            'echo Setting cookies in user environment variables...',
            'echo.',
            ''
        ];

        let cookiesSet = 0;

        if (this.cookies.confluence) {
            lines.push(`echo Setting Confluence cookie...`);
            lines.push(`setx CONFLUENCE_BROWSER_COOKIE "${this.cookies.confluence}"`);
            lines.push('if %ERRORLEVEL% EQU 0 (');
            lines.push('    echo Confluence cookie set successfully');
            lines.push(') else (');
            lines.push('    echo Failed to set Confluence cookie');
            lines.push(')');
            lines.push('echo.');
            cookiesSet++;
        }

        if (this.cookies.jira) {
            lines.push(`echo Setting Jira cookie...`);
            lines.push(`setx JIRA_BROWSER_COOKIE "${this.cookies.jira}"`);
            lines.push('if %ERRORLEVEL% EQU 0 (');
            lines.push('    echo Jira cookie set successfully');
            lines.push(') else (');
            lines.push('    echo Failed to set Jira cookie');
            lines.push(')');
            lines.push('echo.');
            cookiesSet++;
        }

        lines.push('echo ================================================');
        lines.push(`echo  Setup Complete! ${cookiesSet} cookie(s) set`);
        lines.push('echo ================================================');
        lines.push('echo.');
        lines.push('echo IMPORTANT: Close and reopen your command prompt');
        lines.push('echo    for the environment variables to take effect.');
        lines.push('echo.');
        lines.push('echo Environment variables set:');

        if (this.cookies.confluence) {
            lines.push('echo    CONFLUENCE_BROWSER_COOKIE');
        }
        if (this.cookies.jira) {
            lines.push('echo    JIRA_BROWSER_COOKIE');
        }

        lines.push('echo.');
        lines.push('echo You can now run your Python scripts:');
        lines.push('echo    python examples/complete_integration_test.py');
        lines.push('echo    python examples/confluence_with_browser_cookie.py');
        lines.push('echo.');
        lines.push('echo To verify the variables are set, run:');
        lines.push('echo    echo %CONFLUENCE_BROWSER_COOKIE%');
        lines.push('echo    echo %JIRA_BROWSER_COOKIE%');
        lines.push('echo.');
        lines.push('echo 🗑️  To clear these variables later, run:');
        lines.push('echo    setx CONFLUENCE_BROWSER_COOKIE ""');
        lines.push('echo    setx JIRA_BROWSER_COOKIE ""');
        lines.push('echo.');
        lines.push('pause');

        return lines.join('\r\n');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Load previously stored cookies on popup open
    async loadStoredCookies() {
        return new Promise((resolve) => {
            chrome.storage.local.get([
                'confluence_cookie', 'confluence_cookie_timestamp',
                'jira_cookie', 'jira_cookie_timestamp'
            ], (result) => {
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24 hours
                
                let hasStoredCookies = false;

                // Check Confluence cookie
                if (result.confluence_cookie &&
                    result.confluence_cookie_timestamp &&
                    (now - result.confluence_cookie_timestamp) < maxAge) {
                    this.cookies.confluence = result.confluence_cookie;
                    this.displayCookie('confluence', result.confluence_cookie);
                    hasStoredCookies = true;
                }

                // Check Jira cookie
                if (result.jira_cookie &&
                    result.jira_cookie_timestamp &&
                    (now - result.jira_cookie_timestamp) < maxAge) {
                    this.cookies.jira = result.jira_cookie;
                    this.displayCookie('jira', result.jira_cookie);
                    hasStoredCookies = true;
                }

                // Show results if we have stored cookies
                if (hasStoredCookies) {
                    document.getElementById('cookieResults').style.display = 'block';
                    this.showStatus(
                        document.getElementById('extractStatus'),
                        'info',
                        '📋 Stored cookies available (click extract to refresh)'
                    );
                }
                
                this.updateActionButtons();
                resolve();
            });
        });
    }
}

// Initialize when popup loads
document.addEventListener('DOMContentLoaded', async () => {
    const extractor = new CookieExtractor();
    await extractor.loadStoredCookies();
});
