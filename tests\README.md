# Atlassian Client Tests

This directory contains comprehensive tests for the Atlassian Client, with special focus on session authentication and 2FA scenarios.

## Test Files

### 1. `test_session_auth.py` - Comprehensive Session Authentication Tests
Full unittest suite for session authentication functionality:
- Session authentication initialization
- Confluence session authentication
- Browser cookie authentication
- Spaces retrieval with session auth
- Session persistence across requests
- Connection testing

**Run with:**
```bash
python tests/test_session_auth.py
```

### 2. `test_session_simple.py` - Quick Session Authentication Test
Simple, user-friendly test script that provides clear feedback:
- Step-by-step testing process
- Clear success/failure indicators
- Helpful troubleshooting guidance
- 2FA workaround instructions

**Run with:**
```bash
python test_session_simple.py
```

### 3. `test_client.py` - Full Client Test Suite
Comprehensive test suite covering all client functionality:
- Client initialization
- Connectivity testing
- Confluence API operations
- Jira API operations
- Utility functions
- Error handling

**Run with:**
```bash
python tests/test_client.py
```

### 4. `test_auth.py` - Authentication Diagnostics
Low-level authentication testing and diagnostics:
- Basic authentication testing
- Network connectivity checks
- API endpoint testing
- Detailed error analysis

**Run with:**
```bash
python tests/test_auth.py
```

## Session Authentication & 2FA

### Understanding Session Authentication

Session authentication is used when:
- Your Atlassian instance has 2FA enabled
- Personal Access Tokens are not available
- You need to work around authentication restrictions

### Working with 2FA

When 2FA is enabled, you may see authentication failures like:
```
Authentication failed: Authentication failed for https://confluence.smals.be/rest/api/space
```

**Solution: Use Browser Cookie Authentication**

1. **Login to Confluence in your browser**
2. **Extract the session cookie:**
   - Open Developer Tools (F12)
   - Go to Application/Storage → Cookies
   - Find `JSESSIONID` or `CONFLUENCESESSIONID`
   - Copy the cookie value

3. **Set the environment variable:**
   ```bash
   # Windows
   set CONFLUENCE_BROWSER_COOKIE=your_cookie_value_here
   
   # Linux/Mac
   export CONFLUENCE_BROWSER_COOKIE=your_cookie_value_here
   ```

4. **Run the tests again:**
   ```bash
   python test_session_simple.py
   ```

### Expected Test Results

#### ✅ Successful Session Authentication
```
✅ Session authentication is working
✅ Connection to Confluence successful
✅ Confluence API access successful
✅ Jira connection successful
```

#### ⚠️ 2FA Restrictions (Normal with 2FA enabled)
```
✅ Session authentication is working
✅ Connection to Confluence successful
⚠️  Confluence API access limited (may need browser cookie)
✅ Jira connection successful
```

#### ❌ Authentication Issues
```
❌ Session authentication or connection issues
```

## Quick Start

### 1. Basic Test
```bash
# Quick test to verify session authentication
python test_session_simple.py
```

### 2. With Browser Cookie (for 2FA)
```bash
# Set browser cookie and test
set CONFLUENCE_BROWSER_COOKIE=ABC123...
python test_session_simple.py
```

### 3. Full Test Suite
```bash
# Run comprehensive tests
python tests/test_session_auth.py
python tests/test_client.py
```

## Troubleshooting

### Common Issues

1. **"Authentication failed" errors**
   - Usually indicates 2FA is enabled
   - Use browser cookie method
   - Verify credentials in `config.py`

2. **"Connection failed" errors**
   - Check network connectivity
   - Verify URLs in `config.py`
   - Check firewall/proxy settings

3. **"SSL verification failed" errors**
   - Set `verify_ssl: False` in config for testing
   - Install proper SSL certificates for production

### Debug Steps

1. **Check configuration:**
   ```python
   from config import ATLASSIAN_CONFIG
   print(ATLASSIAN_CONFIG)
   ```

2. **Test network connectivity:**
   ```bash
   python tests/test_auth.py
   ```

3. **Run with debug logging:**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   # Then run your test
   ```

## Configuration

Ensure your `config.py` is properly configured:

```python
ATLASSIAN_CONFIG = {
    "CONFLUENCE_URL": "https://your-confluence-server.com",
    "CONFLUENCE_USERNAME": "<EMAIL>",
    "CONFLUENCE_API_TOKEN": "your_token_or_password",
    "JIRA_URL": "https://your-jira-server.com", 
    "JIRA_USERNAME": "<EMAIL>",
    "JIRA_API_TOKEN": "your_token_or_password"
}

AUTH_METHOD = 'session'  # For session authentication
```

## Test Output Examples

### Successful Test Output
```
🔧 Step 1: Initializing Atlassian client...
   ✓ Client initialized successfully

🔗 Step 2: Testing connection...
   Confluence: ✓
   Jira: ✓

📚 Step 3: Testing Confluence spaces retrieval...
   ✓ Successfully retrieved 5 spaces
   📁 Available spaces:
      1. DEMO - Demo Space
      2. PROJ - Project Documentation
      ...
```

### 2FA Restricted Output
```
📚 Step 3: Testing Confluence spaces retrieval...
   ✗ Authentication failed: Authentication failed for https://confluence.smals.be/rest/api/space
   💡 This is likely due to 2FA being enabled
   💡 To bypass 2FA, extract JSESSIONID cookie from browser
```

## Next Steps

After successful testing:
1. Use the client in your applications
2. Implement proper error handling
3. Consider caching session cookies for production use
4. Monitor session expiration and renewal
