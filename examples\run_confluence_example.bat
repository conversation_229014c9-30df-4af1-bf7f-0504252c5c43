@echo off
REM Windows batch script to run Confluence session cookie example
REM 
REM Usage:
REM   run_confluence_example.bat your_cookie_value_here
REM
REM Or set the environment variable manually and run without arguments:
REM   set CONFLUENCE_BROWSER_COOKIE=your_cookie_value
REM   run_confluence_example.bat

echo.
echo ========================================
echo Confluence Session Cookie Example
echo ========================================
echo.

REM Check if cookie value was provided as argument
if "%1"=="" (
    if "%CONFLUENCE_BROWSER_COOKIE%"=="" (
        echo ERROR: No cookie value provided!
        echo.
        echo Usage Option 1 - Command line argument:
        echo   run_confluence_example.bat your_cookie_value_here
        echo.
        echo Usage Option 2 - Environment variable:
        echo   set CONFLUENCE_BROWSER_COOKIE=your_cookie_value
        echo   run_confluence_example.bat
        echo.
        echo To get your cookie value:
        echo 1. Login to Confluence in your browser
        echo 2. Open Developer Tools (F12)
        echo 3. Go to Application/Storage tab - Cookies
        echo 4. Find CONFLUENCESESSIONID or JSESSIONID
        echo 5. Copy the cookie value
        echo.
        pause
        exit /b 1
    )
) else (
    set CONFLUENCE_BROWSER_COOKIE=%1
)

echo Cookie set: %CONFLUENCE_BROWSER_COOKIE:~0,20%...
echo.

REM Run quick test first
echo Running quick test...
python test_confluence_session.py
if errorlevel 1 (
    echo.
    echo Quick test failed! Please check your cookie and configuration.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Running full Confluence example...
echo ========================================
echo.

REM Run the full example
python confluence_usage.py

echo.
echo ========================================
echo Example completed!
echo ========================================
pause
