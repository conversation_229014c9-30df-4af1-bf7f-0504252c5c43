#!/usr/bin/env python3
"""
MCP Server Example: Confluence Space Search
This example demonstrates how to use the MCP server to search for Confluence spaces
using session authentication with browser cookies.

Usage:
    python examples/mcp_confluence_space_search.py "space_name"
    
Example:
    python examples/mcp_confluence_space_search.py "Development"
    python examples/mcp_confluence_space_search.py "HR"
"""

import sys
import os
import json
import asyncio
from pathlib import Path

# Add the mcp-server directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# Path to MCP configuration
MCP_CONFIG_PATH = Path(__file__).parent.parent / "mcp-server" / "mcp_config_env.json"

def load_mcp_config():
    """Load MCP server configuration from JSON file"""
    try:
        with open(MCP_CONFIG_PATH, 'r') as f:
            config = json.load(f)
        return config['mcpServers']['atlassian']
    except Exception as e:
        print(f"Error loading MCP config from {MCP_CONFIG_PATH}: {e}")
        return None

def validate_space_name(space_name):
    """Validate the space name parameter"""
    if not space_name:
        print("Error: Space name cannot be empty")
        return False

    if len(space_name.strip()) < 2:
        print("Error: Space name must be at least 2 characters long")
        return False

    return True

async def search_confluence_spaces(space_name):
    """Search for Confluence spaces using MCP server"""
    print(f"Searching for Confluence spaces matching: '{space_name}'")
    print("-" * 50)

    # Load MCP configuration
    config = load_mcp_config()
    if not config:
        return False

    # Prepare server parameters
    server_params = StdioServerParameters(
        command=config['command'],
        args=config['args'],
        env=config['env']
    )

    try:
        # Connect to MCP server
        print("Connecting to MCP server...")
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("Connected to MCP server")

                # Initialize the session
                await session.initialize()

                # Get all spaces first
                print("Retrieving Confluence spaces...")
                spaces_result = await session.call_tool(
                    "confluence_get_spaces",
                    {"limit": 100, "expand": "description"}
                )
                
                if not spaces_result.content:
                    print("No response from confluence_get_spaces tool")
                    return False

                # Parse the spaces data
                spaces_data = json.loads(spaces_result.content[0].text)
                all_spaces = spaces_data.get('results', [])

                if not all_spaces:
                    print("No spaces found in Confluence")
                    return False

                print(f"Total spaces available: {len(all_spaces)}")

                # Filter spaces by name (case-insensitive partial match)
                search_term = space_name.lower()
                matching_spaces = []

                for space in all_spaces:
                    space_name_lower = space.get('name', '').lower()
                    space_key_lower = space.get('key', '').lower()

                    if (search_term in space_name_lower or
                        search_term in space_key_lower):
                        matching_spaces.append(space)

                # Display results
                print(f"\nFound {len(matching_spaces)} matching space(s):")
                print("-" * 50)
                
                if not matching_spaces:
                    print(f"No spaces found matching '{space_name}'")
                    print("\nAvailable spaces:")
                    for space in all_spaces[:10]:  # Show first 10 spaces
                        print(f"  {space.get('key', 'N/A')}: {space.get('name', 'N/A')}")
                    if len(all_spaces) > 10:
                        print(f"  ... and {len(all_spaces) - 10} more spaces")
                    return False
                
                # Display matching spaces with details
                for i, space in enumerate(matching_spaces, 1):
                    print(f"\n{i}. Space: {space.get('name', 'N/A')}")
                    print(f"   Key: {space.get('key', 'N/A')}")
                    print(f"   Type: {space.get('type', 'N/A')}")
                    print(f"   URL: {config['env']['CONFLUENCE_URL']}/spaces/{space.get('key', '')}")

                    # Show description if available
                    description = space.get('description', {})
                    if description and description.get('plain', {}).get('value'):
                        desc_text = description['plain']['value'][:100]
                        print(f"   Description: {desc_text}{'...' if len(desc_text) == 100 else ''}")

                    # Get additional space details
                    try:
                        space_details = await session.call_tool(
                            "confluence_search",
                            {
                                "cql": f"space = {space.get('key')} AND type = page",
                                "limit": 1
                            }
                        )

                        if space_details.content:
                            details_data = json.loads(space_details.content[0].text)
                            page_count = details_data.get('size', 0)
                            print(f"   Pages: ~{page_count} pages")
                    except Exception as e:
                        print(f"   Pages: Unable to count ({str(e)[:50]}...)")

                print(f"\nSearch completed successfully!")
                return True
                
    except Exception as e:
        print(f"Error during MCP server communication: {e}")
        return False

def show_usage():
    """Show usage instructions"""
    print("Confluence Space Search via MCP Server")
    print("-" * 40)
    print()
    print("Usage:")
    print("  python examples/mcp_confluence_space_search.py <space_name>")
    print()
    print("Examples:")
    print("  python examples/mcp_confluence_space_search.py \"Development\"")
    print("  python examples/mcp_confluence_space_search.py \"HR\"")
    print("  python examples/mcp_confluence_space_search.py \"Project\"")
    print()
    print("Requirements:")
    print("  - MCP server configuration in mcp-server/mcp_config_env.json")
    print("  - Valid CONFLUENCE_BROWSER_COOKIE in the configuration")
    print("  - Session authentication enabled (ATLASSIAN_AUTH_METHOD=session)")
    print()

async def main():
    """Main function"""
    print("Confluence Space Search via MCP Server")
    print("-" * 40)

    # Check command line arguments
    if len(sys.argv) != 2:
        print("Error: Space name parameter required")
        print()
        show_usage()
        return False

    space_name = sys.argv[1].strip()

    # Validate space name
    if not validate_space_name(space_name):
        show_usage()
        return False

    # Check if MCP config exists
    if not MCP_CONFIG_PATH.exists():
        print(f"Error: MCP configuration file not found at {MCP_CONFIG_PATH}")
        print("Please ensure the MCP server is properly configured.")
        return False

    # Perform the search
    success = await search_confluence_spaces(space_name)

    if success:
        print("\nNext steps:")
        print("  - Use the space key to search for specific pages")
        print("  - Access the space URL to browse content")
        print("  - Use other MCP tools to interact with the space")

    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n❌ Search cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
