#!/usr/bin/env python3
"""
Simple test script for session authentication and Confluence spaces

This is a standalone script to quickly test:
1. Session authentication setup
2. Confluence spaces retrieval
3. Browser cookie authentication (if available)

Usage:
    python test_session_simple.py

For 2FA environments, set browser cookie:
    set CONFLUENCE_BROWSER_COOKIE=<your_jsessionid_value>
    python test_session_simple.py
"""

import os
import sys
import logging
from datetime import datetime

# Add current directory to path to import client
sys.path.insert(0, os.path.dirname(__file__))

from client import AtlassianClient, ConfluenceAPI, AuthenticationError, APIError
from config import ATLASSIAN_CONFIG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_session_authentication():
    """Test session authentication and spaces retrieval"""
    print("=" * 70)
    print("ATLASSIAN SESSION AUTHENTICATION TEST")
    print("=" * 70)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Configuration
    config = ATLASSIAN_CONFIG.copy()
    client_settings = {
        'timeout': 30,
        'verify_ssl': True,
        'auth_method': 'session'
    }
    
    print("📋 Configuration:")
    print(f"   Confluence URL: {config['CONFLUENCE_URL']}")
    print(f"   Username: {config['CONFLUENCE_USERNAME']}")
    print(f"   Auth method: session")
    print()
    
    # Check for browser cookie
    browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
    if browser_cookie:
        print(f"🍪 Browser cookie found: {browser_cookie[:20]}...")
    else:
        print("🍪 No browser cookie found (CONFLUENCE_BROWSER_COOKIE env var)")
    print()
    
    try:
        # Step 1: Initialize client
        print("🔧 Step 1: Initializing Atlassian client...")
        client = AtlassianClient(config, **client_settings)
        print("   ✓ Client initialized successfully")
        print()
        
        # Step 2: Test connection
        print("🔗 Step 2: Testing connection...")
        connection_status = client.test_connection()
        
        confluence_ok = connection_status.get('confluence', False)
        jira_ok = connection_status.get('jira', False)
        
        print(f"   Confluence: {'✓' if confluence_ok else '✗'}")
        print(f"   Jira: {'✓' if jira_ok else '✗'}")
        print()
        
        if not confluence_ok:
            print("⚠️  Confluence connection failed - this may be due to 2FA")
            if not browser_cookie:
                print("💡 Try setting CONFLUENCE_BROWSER_COOKIE environment variable")
            print()
        
        # Step 3: Test Confluence API
        print("📚 Step 3: Testing Confluence spaces retrieval...")
        confluence = ConfluenceAPI(client)
        
        try:
            spaces = confluence.get_spaces(limit=10)
            
            if 'results' in spaces:
                space_count = len(spaces['results'])
                print(f"   ✓ Successfully retrieved {space_count} spaces")
                
                if space_count > 0:
                    print("   📁 Available spaces:")
                    for i, space in enumerate(spaces['results'][:5]):  # Show first 5
                        print(f"      {i+1}. {space['key']} - {space['name']}")
                    
                    if space_count > 5:
                        print(f"      ... and {space_count - 5} more")
                    
                    # Test getting a specific space
                    print()
                    print("🔍 Step 4: Testing specific space retrieval...")
                    first_space_key = spaces['results'][0]['key']
                    
                    try:
                        space_details = confluence.get_space(first_space_key, expand='description')
                        print(f"   ✓ Successfully retrieved details for space: {first_space_key}")
                        print(f"      Name: {space_details.get('name', 'N/A')}")
                        if 'description' in space_details:
                            desc = space_details['description'].get('plain', {}).get('value', 'No description')
                            print(f"      Description: {desc[:100]}{'...' if len(desc) > 100 else ''}")
                    except Exception as e:
                        print(f"   ✗ Failed to get space details: {e}")
                
                else:
                    print("   ⚠️  No spaces found (user may not have access)")
            else:
                print("   ✗ Invalid response format")
                
        except AuthenticationError as e:
            print(f"   ✗ Authentication failed: {e}")
            print("   💡 This is likely due to 2FA being enabled")
            if not browser_cookie:
                print("   💡 To bypass 2FA, extract JSESSIONID cookie from browser:")
                print("      1. Login to Confluence in browser")
                print("      2. Open Developer Tools (F12)")
                print("      3. Go to Application/Storage > Cookies")
                print("      4. Find JSESSIONID or CONFLUENCESESSIONID")
                print("      5. Copy the value and set: CONFLUENCE_BROWSER_COOKIE=<value>")
        except APIError as e:
            print(f"   ✗ API error: {e}")
        except Exception as e:
            print(f"   ✗ Unexpected error: {e}")
        
        print()
        print("=" * 70)
        print("TEST SUMMARY")
        print("=" * 70)
        
        if confluence_ok:
            print("✅ Session authentication is working")
            print("✅ Connection to Confluence successful")
            try:
                # Quick test to see if we can actually get data
                test_spaces = confluence.get_spaces(limit=1)
                if 'results' in test_spaces:
                    print("✅ Confluence API access successful")
                else:
                    print("⚠️  Confluence API access limited (may need browser cookie)")
            except:
                print("⚠️  Confluence API access limited (may need browser cookie)")
        else:
            print("❌ Session authentication or connection issues")
        
        if jira_ok:
            print("✅ Jira connection successful")
        else:
            print("⚠️  Jira connection failed")
        
        print()
        if browser_cookie:
            print("💡 Using browser cookie for authentication")
        else:
            print("💡 Using username/token for session authentication")
            print("💡 If you encounter 2FA issues, use browser cookie method")
        
        print()
        print("🎯 Next steps:")
        print("   - If tests pass: Your session authentication is working!")
        print("   - If 2FA issues: Set CONFLUENCE_BROWSER_COOKIE environment variable")
        print("   - For production: Consider using Personal Access Tokens if available")
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        logger.exception("Detailed error information:")
        return False
    
    return True


if __name__ == "__main__":
    try:
        success = test_session_authentication()
        print()
        if success:
            print("🎉 Test completed successfully!")
        else:
            print("💥 Test completed with errors")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
