#!/bin/bash
# Shell script to run Confluence session cookie example
# 
# Usage:
#   ./run_confluence_example.sh your_cookie_value_here
#
# Or set the environment variable manually and run without arguments:
#   export CONFLUENCE_BROWSER_COOKIE=your_cookie_value
#   ./run_confluence_example.sh

echo
echo "========================================"
echo "Confluence Session Cookie Example"
echo "========================================"
echo

# Check if cookie value was provided as argument
if [ -z "$1" ]; then
    if [ -z "$CONFLUENCE_BROWSER_COOKIE" ]; then
        echo "ERROR: No cookie value provided!"
        echo
        echo "Usage Option 1 - Command line argument:"
        echo "  ./run_confluence_example.sh your_cookie_value_here"
        echo
        echo "Usage Option 2 - Environment variable:"
        echo "  export CONFLUENCE_BROWSER_COOKIE=your_cookie_value"
        echo "  ./run_confluence_example.sh"
        echo
        echo "To get your cookie value:"
        echo "1. Login to Confluence in your browser"
        echo "2. Open Developer Tools (F12)"
        echo "3. Go to Application/Storage tab → Cookies"
        echo "4. Find CONFLUENCESESSIONID or JSESSIONID"
        echo "5. Copy the cookie value"
        echo
        exit 1
    fi
else
    export CONFLUENCE_BROWSER_COOKIE="$1"
fi

echo "Cookie set: ${CONFLUENCE_BROWSER_COOKIE:0:20}..."
echo

# Run quick test first
echo "Running quick test..."
python3 test_confluence_session.py
if [ $? -ne 0 ]; then
    echo
    echo "Quick test failed! Please check your cookie and configuration."
    exit 1
fi

echo
echo "========================================"
echo "Running full Confluence example..."
echo "========================================"
echo

# Run the full example
python3 confluence_usage.py

echo
echo "========================================"
echo "Example completed!"
echo "========================================"
