#!/usr/bin/env python3
"""
Examples of using Confluence tools with session cookie authentication to work around 2FA

This example demonstrates:
1. How to set up session cookie authentication to bypass 2FA
2. How to retrieve all Confluence spaces
3. How to get detailed space information
4. How to search for content within spaces

Usage:
1. <PERSON>gin to Confluence in your browser
2. Extract the JSESSIONID cookie value (see instructions below)
3. Set environment variable: set CONFLUENCE_BROWSER_COOKIE=<cookie_value>
4. Run: python confluence_usage.py

Cookie Extraction Instructions:
1. Open your browser and login to Confluence
2. Open Developer Tools (F12)
3. Go to Application/Storage tab
4. Find Cookies for your Confluence domain
5. Look for 'CONFLUENCESESSIONID' or 'JSESSIONID'
6. Copy the cookie value
"""

import sys
import os
from datetime import datetime

# Add paths to import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from extended_atlassian_client import ExtendedAtlassianClient
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

def show_cookie_extraction_instructions():
    """Display instructions for extracting browser cookies"""
    print("📋 HOW TO EXTRACT BROWSER COOKIE:")
    print("="*50)
    print("1. Open your browser and login to Confluence")
    print("2. Open Developer Tools (F12)")
    print("3. Go to Application/Storage tab → Cookies")
    print("4. Find your Confluence domain")
    print("5. Look for 'CONFLUENCESESSIONID' or 'JSESSIONID'")
    print("6. Copy the cookie value")
    print("7. Set environment variable:")
    print("   Windows: set CONFLUENCE_BROWSER_COOKIE=<cookie_value>")
    print("   Linux/Mac: export CONFLUENCE_BROWSER_COOKIE=<cookie_value>")
    print("8. Run this script again")
    print()

def test_connection():
    """Test the connection to Confluence"""
    print("🔗 Testing Confluence Connection")
    print("="*40)
    
    try:
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        results = client.test_connection()
        
        if results.get('confluence'):
            print("✅ Connection successful!")
            return client
        else:
            print("❌ Connection failed!")
            return None
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None

def get_all_spaces(client, limit=50):
    """Get all Confluence spaces"""
    print(f"📚 Getting All Confluence Spaces (limit: {limit})")
    print("="*40)
    
    try:
        spaces_data = client.confluence.get_spaces(limit=limit, expand="description,homepage")
        spaces = spaces_data.get('results', [])
        total = spaces_data.get('size', len(spaces))
        
        print(f"Found {total} spaces:")
        print()
        
        for i, space in enumerate(spaces, 1):
            key = space.get('key', 'N/A')
            name = space.get('name', 'N/A')
            space_type = space.get('type', 'N/A')
            
            print(f"{i:2d}. {key} - {name}")
            print(f"    Type: {space_type}")
            
            # Show description if available
            description = space.get('description', {})
            if description and 'plain' in description:
                desc_text = description['plain']['value'][:100]
                if len(desc_text) == 100:
                    desc_text += "..."
                print(f"    Description: {desc_text}")
            
            # Show homepage if available
            homepage = space.get('homepage', {})
            if homepage:
                homepage_title = homepage.get('title', 'N/A')
                print(f"    Homepage: {homepage_title}")
            
            print()
        
        return spaces
        
    except Exception as e:
        print(f"❌ Error getting spaces: {e}")
        return []

def get_space_details(client, space_key):
    """Get detailed information about a specific space"""
    print(f"🔍 Getting Details for Space: {space_key}")
    print("="*40)
    
    try:
        space = client.confluence.get_space(space_key, expand="description,homepage,metadata")
        
        print(f"Key: {space.get('key', 'N/A')}")
        print(f"Name: {space.get('name', 'N/A')}")
        print(f"Type: {space.get('type', 'N/A')}")
        
        # Description
        description = space.get('description', {})
        if description and 'plain' in description:
            print(f"Description: {description['plain']['value']}")
        
        # Homepage
        homepage = space.get('homepage', {})
        if homepage:
            print(f"Homepage: {homepage.get('title', 'N/A')} (ID: {homepage.get('id', 'N/A')})")
        
        # Metadata
        metadata = space.get('metadata', {})
        if metadata:
            print("Metadata:")
            for key, value in metadata.items():
                print(f"  {key}: {value}")
        
        return space
        
    except Exception as e:
        print(f"❌ Error getting space details for {space_key}: {e}")
        return None

def search_space_content(client, space_key, limit=10):
    """Search for content within a specific space"""
    print(f"🔍 Searching Content in Space: {space_key}")
    print("="*40)
    
    try:
        # Search for pages in the space
        cql = f"space = {space_key} AND type = page"
        results = client.confluence.search(cql=cql, limit=limit, expand="version,space")
        
        content_items = results.get('results', [])
        total = results.get('totalSize', len(content_items))
        
        print(f"Found {total} pages in space {space_key} (showing {len(content_items)}):")
        print()
        
        for i, item in enumerate(content_items, 1):
            title = item.get('title', 'N/A')
            content_type = item.get('type', 'N/A')
            item_id = item.get('id', 'N/A')
            
            print(f"{i:2d}. {title}")
            print(f"    Type: {content_type} | ID: {item_id}")
            
            # Version info
            version = item.get('version', {})
            if version:
                version_num = version.get('number', 'N/A')
                when = version.get('when', 'N/A')
                print(f"    Version: {version_num} | Last modified: {when}")
            
            print()
        
        return content_items
        
    except Exception as e:
        print(f"❌ Error searching content in space {space_key}: {e}")
        return []

def main():
    """Main function to demonstrate Confluence usage with session cookies"""
    print("🌐 Confluence Usage Examples with Session Cookie Authentication")
    print("="*70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check for browser cookie
    browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
    
    if not browser_cookie:
        print("❌ No CONFLUENCE_BROWSER_COOKIE environment variable found!")
        print()
        show_cookie_extraction_instructions()
        return False
    
    print(f"✅ Browser cookie found: {browser_cookie[:20]}...")
    print()
    
    # Test connection
    client = test_connection()
    if not client:
        print("❌ Cannot proceed without a valid connection")
        return False
    
    print("\n" + "="*70)
    
    # Example 1: Get all spaces
    spaces = get_all_spaces(client, limit=20)
    
    if spaces:
        print("\n" + "="*70)
        
        # Example 2: Get details for the first space
        first_space = spaces[0]
        space_key = first_space.get('key')
        if space_key:
            get_space_details(client, space_key)
            
            print("\n" + "="*70)
            
            # Example 3: Search content in the first space
            search_space_content(client, space_key, limit=5)
    
    print("\n🎉 Examples completed!")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
