{"mcpServers": {"atlassian": {"command": "python", "args": ["C:\\dev\\prj\\atlassian-companion\\mcp-server\\server.py"], "env": {"PYTHONPATH": "C:\\dev\\prj\\atlassian-companion", "CONFLUENCE_URL": "https://confluence.smals.be", "CONFLUENCE_USERNAME": "<EMAIL>", "CONFLUENCE_API_TOKEN": "not-needed-for-session-auth", "JIRA_URL": "https://jira.smals.be", "JIRA_USERNAME": "<EMAIL>", "JIRA_API_TOKEN": "not-needed-for-session-auth", "ATLASSIAN_AUTH_METHOD": "session", "ATLASSIAN_VERIFY_SSL": "true", "ATLASSIAN_TIMEOUT": "30", "ATLASSIAN_MAX_RETRIES": "3", "CONFLUENCE_BROWSER_COOKIE": "BB612E25432B450571C3F8A15764DD92", "JIRA_BROWSER_COOKIE": "38F6007781E5BED39071E3AC35A9DBF9"}}}}